{"files.associations": {"chrono": "cpp", "xloctime": "cpp", "bitset": "cpp", "string": "cpp", "memory": "cpp", "stack": "cpp", "ostream": "cpp", "cmath": "cpp", "algorithm": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "cfenv": "cpp", "charconv": "cpp", "cinttypes": "cpp", "clocale": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "execution": "cpp", "filesystem": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory_resource": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "queue": "cpp", "random": "cpp", "ranges": "cpp", "ratio": "cpp", "regex": "cpp", "scoped_allocator": "cpp", "set": "cpp", "shared_mutex": "cpp", "source_location": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "strstream": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "valarray": "cpp", "variant": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xmemory": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "geometry": "cpp", "dense": "cpp", "print": "cpp"}, "C_Cpp.default.cppStandard": "c++20", "C_Cpp.default.compilerPath": "cl.exe", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64"}